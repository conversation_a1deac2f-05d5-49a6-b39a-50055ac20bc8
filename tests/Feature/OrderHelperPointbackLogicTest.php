<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Services\pattern\OrderHelper;
use App\Services\pattern\BonusHelper;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;

class OrderHelperPointbackLogicTest extends TestCase
{
    /**
     * 測試投資商品廣告邏輯判斷
     * 
     * @return void
     */
    public function test_investment_product_ad_logic()
    {
        // 模擬投資商品 + 廣告商品的情況
        $product = [
            'product_cate' => 1,  // 投資商品
            'use_ad' => 1,        // 廣告商品
            'bonus_model_id' => 1,
            'price_cv' => 1000
        ];
        
        $newVipLevel = 0;  // 無課程級別
        
        // 測試邏輯：投資商品且廣告商品應該進入總部消費回饋
        $shouldUseAdLogic = false;
        
        if ($product['product_cate'] == 1 && $product['use_ad'] == 1) {
            $shouldUseAdLogic = true;
        } elseif ($newVipLevel == 2 && $product['use_ad'] == 1) {
            $shouldUseAdLogic = true;
        }
        
        $this->assertTrue($shouldUseAdLogic, '投資商品且廣告商品應該進入總部消費回饋邏輯');
    }

    /**
     * 測試任督級別廣告商品邏輯判斷
     * 
     * @return void
     */
    public function test_rendu_level_ad_logic()
    {
        // 模擬消費商品 + 任督級別 + 廣告商品的情況
        $product = [
            'product_cate' => 0,  // 消費商品
            'use_ad' => 1,        // 廣告商品
            'bonus_model_id' => 1,
            'price_cv' => 1000
        ];
        
        $newVipLevel = 2;  // 任督級別
        
        // 測試邏輯：任督級別且廣告商品應該進入總部消費回饋
        $shouldUseAdLogic = false;
        
        if ($product['product_cate'] == 1 && $product['use_ad'] == 1) {
            $shouldUseAdLogic = true;
        } elseif ($newVipLevel == 2 && $product['use_ad'] == 1) {
            $shouldUseAdLogic = true;
        }
        
        $this->assertTrue($shouldUseAdLogic, '任督級別且廣告商品應該進入總部消費回饋邏輯');
    }

    /**
     * 測試任督以上級別一般分潤邏輯判斷
     * 
     * @return void
     */
    public function test_above_rendu_level_normal_logic()
    {
        // 模擬消費商品 + 中脈級別 + 廣告商品的情況
        $product = [
            'product_cate' => 0,  // 消費商品
            'use_ad' => 1,        // 廣告商品
            'bonus_model_id' => 1,
            'price_cv' => 1000
        ];
        
        $newVipLevel = 3;  // 中脈級別（任督以上）
        
        // 測試邏輯：任督以上級別一律進行一般分潤回饋
        $shouldUseAdLogic = false;
        
        if ($product['product_cate'] == 1 && $product['use_ad'] == 1) {
            $shouldUseAdLogic = true;
        } elseif ($newVipLevel == 2 && $product['use_ad'] == 1) {
            $shouldUseAdLogic = true;
        }
        
        $this->assertFalse($shouldUseAdLogic, '任督以上級別應該進行一般分潤回饋，不進入總部消費回饋邏輯');
    }

    /**
     * 測試推廣獎勵分配方式判斷 - 消費商品
     * 
     * @return void
     */
    public function test_promotion_reward_distribution_consumption_product()
    {
        $product = ['product_cate' => 0]; // 消費商品
        $newVipLevel = 3;
        $registration_from = 2; // 廣告來源
        
        // 判斷推廣獎勵分配方式
        $usePartnerDistribution = false;
        
        if ($product['product_cate'] != 1) {
            // 消費商品：推廣獎勵一律個人拿（直推邏輯）
            $usePartnerDistribution = false;
        } elseif ($newVipLevel > 2) {
            // 任督以上級別：根據註冊來源決定
            $usePartnerDistribution = ($registration_from == 2);
        } elseif ($newVipLevel > 0 && $registration_from == 2) {
            // 原有邏輯：有課程級別且廣告來源
            $usePartnerDistribution = true;
        }
        
        $this->assertFalse($usePartnerDistribution, '消費商品推廣獎勵應該個人拿');
    }

    /**
     * 測試推廣獎勵分配方式判斷 - 任督以上級別廣告來源
     * 
     * @return void
     */
    public function test_promotion_reward_distribution_above_rendu_ad_source()
    {
        $product = ['product_cate' => 1]; // 投資商品
        $newVipLevel = 3; // 中脈級別
        $registration_from = 2; // 廣告來源
        
        // 判斷推廣獎勵分配方式
        $usePartnerDistribution = false;
        
        if ($product['product_cate'] != 1) {
            $usePartnerDistribution = false;
        } elseif ($newVipLevel > 2) {
            // 任督以上級別：根據註冊來源決定
            $usePartnerDistribution = ($registration_from == 2);
        } elseif ($newVipLevel > 0 && $registration_from == 2) {
            $usePartnerDistribution = true;
        }
        
        $this->assertTrue($usePartnerDistribution, '任督以上級別廣告來源應該讓全體有效合伙人拿');
    }

    /**
     * 測試推廣獎勵分配方式判斷 - 任督以上級別直推來源
     * 
     * @return void
     */
    public function test_promotion_reward_distribution_above_rendu_direct_source()
    {
        $product = ['product_cate' => 1]; // 投資商品
        $newVipLevel = 3; // 中脈級別
        $registration_from = 1; // 直推來源
        
        // 判斷推廣獎勵分配方式
        $usePartnerDistribution = false;
        
        if ($product['product_cate'] != 1) {
            $usePartnerDistribution = false;
        } elseif ($newVipLevel > 2) {
            // 任督以上級別：根據註冊來源決定
            $usePartnerDistribution = ($registration_from == 2);
        } elseif ($newVipLevel > 0 && $registration_from == 2) {
            $usePartnerDistribution = true;
        }
        
        $this->assertFalse($usePartnerDistribution, '任督以上級別直推來源應該推薦者個人拿');
    }
}
